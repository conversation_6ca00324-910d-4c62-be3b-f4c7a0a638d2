import React, { useRef, useState, useEffect } from "react";
import AppLayout from "../layout/AppLayout";
import { Link, usePage } from "@inertiajs/react";

// Tab menu items configuration
const tabItems = [
    {
        key: "all-products",
        label: "All Products",
        path: "/v2/products/all-products",
    },
    {
        key: "importcsv",
        label: "Import",
        path: "/v2/products/importcsv",
    },
    {
        key: "export",
        label: "Export",
        path: "/v2/products/export",
    },
    {
        key: "categories",
        label: "Categories",
        path: "/v2/products/categories",
    },
    {
        key: "attribute-sets",
        label: "Attribute Sets",
        path: "/v2/products/attribute-sets",
    },
    {
        key: "attribute",
        label: "Attribute",
        path: "/v2/products/attribute",
    },
    {
        key: "variant-options",
        label: "Variant Options",
        path: "/v2/products/variant-options",
    },
    {
        key: "brands",
        label: "Brands",
        path: "/v2/products/brands",
    },
    {
        key: "vendors",
        label: "Vendors",
        path: "/v2/products/vendors",
    },
    {
        key: "languages",
        label: "Languages",
        path: "/v2/products/languages",
    },
    {
        key: "locations",
        label: "Locations",
        path: "/v2/products/locations",
    },
    {
        key: "stores",
        label: "Stores",
        path: "/v2/products/stores",
    },
];

const ProductsLayout = ({ children, title, activeTab, hideHeader = false }) => {
    const { props } = usePage();
    const currentTab = activeTab ?? (props.tab || "all-products");
    const scrollContainerRef = useRef(null);
    const [showScrollButtons, setShowScrollButtons] = useState(false);

    useEffect(() => {
        const checkScroll = () => {
            if (scrollContainerRef.current) {
                const { scrollWidth, clientWidth } = scrollContainerRef.current;
                setShowScrollButtons(scrollWidth > clientWidth);
            }
        };

        checkScroll();
        window.addEventListener("resize", checkScroll);
        return () => window.removeEventListener("resize", checkScroll);
    }, []);

    const scroll = (direction) => {
        if (scrollContainerRef.current) {
            const scrollAmount = 200;
            scrollContainerRef.current.scrollLeft += direction === "left" ? -scrollAmount : scrollAmount;
        }
    };

    console.log(props,currentTab);
    return (

        <AppLayout
            activeMenuItem="products"
            title={title}
            showHeader={!hideHeader}>

            {/* Tab Navigation - Only show when hideHeader is false */}
            {!hideHeader && (
                <div className="bg-white border-b border-[#DBDBDB] px-[30px] h-[36px] flex items-center relative">
                    {showScrollButtons && (
                        <button
                            onClick={() => scroll("left")}
                            className="absolute left-0 z-10 h-full px-2 bg-white bg-opacity-90 hover:bg-opacity-100"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>
                    )}

                    <div ref={scrollContainerRef} className="flex h-full overflow-x-auto scrollbar-hide" style={{ scrollBehavior: "smooth" }}>
                        <div className="flex h-full">
                            {tabItems.map((item) => {
                                const isActive = item.key === currentTab;
                                return (
                                    <Link
                                        key={item.key}
                                        href={item.path}
                                        className={`h-[36px] flex-shrink-0 flex items-center px-4 relative ${
                                            isActive
                                                ? "text-[#740898] font-[700] border-b-2 border-[#740898]"
                                                : "text-[#262626] hover:text-[#740898]"
                                        }`}
                                    >
                                        {item.label}
                                    </Link>
                                );
                            })}
                        </div>
                    </div>

                    {showScrollButtons && (
                        <button
                            onClick={() => scroll("right")}
                            className="absolute right-0 z-10 h-full px-2 bg-white bg-opacity-90 hover:bg-opacity-100"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    )}
                </div>
            )}

            {/* Content - Adjust padding when tabs are hidden */}
            <div className={`flex-grow min-h-screen bg-[#f9fafb] ${hideHeader ? 'pt-0' : 'p-4'}`}>
                {children}
            </div>
        </AppLayout>
    );
};

export default ProductsLayout;
