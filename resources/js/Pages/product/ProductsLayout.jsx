import React, { useRef, useState, useEffect } from "react";
import AppLayout from "../layout/AppLayout";
import { Link, usePage } from "@inertiajs/react";

// Tab menu items configuration
const tabItems = [
    {
        key: "all-products",
        label: "All Products",
        path: "/products/all-products",
    },
    {
        key: "importcsv",
        label: "Import",
        path: "/products/importcsv",
    },
    {
        key: "export",
        label: "Export",
        path: "/products/export/step1",
    },
    {
        key: "categories",
        label: "Categories",
        path: "/products/categories",
    },
    {
        key: "attribute-sets",
        label: "Attribute Sets",
        path: "/products/family",
    },
    {
        key: "attribute",
        label: "Attribute",
        path: "/products/attributes",
    },
    {
        key: "variant-options",
        label: "Variant Options",
        path: "/products/variant-attribute",
    },
    {
        key: "brands",
        label: "Brands",
        path: "/products/brands",
    },
    {
        key: "vendors",
        label: "Vendors",
        path: "/products/vendors",
    },
    {
        key: "languages",
        label: "Languages",
        path: "/products/versions",
    },
    {
        key: "locations",
        label: "Locations",
        path: "/products/locations",
    },
    {
        key: "stores",
        label: "Stores",
        path: "/channel",
    },
];

const ProductsLayout = ({ children, title, activeTab, hideHeader = false }) => {
    const { props } = usePage();
    const currentTab = activeTab ?? (props.tab || "all-products");
    const scrollContainerRef = useRef(null);
    const [showScrollButtons, setShowScrollButtons] = useState(false);

    useEffect(() => {
        const checkScroll = () => {
            if (scrollContainerRef.current) {
                const { scrollWidth, clientWidth } = scrollContainerRef.current;
                setShowScrollButtons(scrollWidth > clientWidth);
            }
        };

        checkScroll();
        window.addEventListener("resize", checkScroll);
        return () => window.removeEventListener("resize", checkScroll);
    }, []);

    const scroll = (direction) => {
        if (scrollContainerRef.current) {
            const scrollAmount = 200;
            scrollContainerRef.current.scrollLeft += direction === "left" ? -scrollAmount : scrollAmount;
        }
    };

    console.log(props,currentTab);
    return (

        <AppLayout
            activeMenuItem="products"
            title={title}
            showHeader={!hideHeader}>

            {/* Tab Navigation - Only show when hideHeader is false */}
            {!hideHeader && (
                <div className="bg-white border-b border-[#DBDBDB] px-2 sm:px-4 md:px-6 lg:px-[30px] h-10 sm:h-12 md:h-[36px] flex items-center relative">
                    {/* Left scroll button - hidden on mobile, visible on larger screens when needed */}
                    {showScrollButtons && (
                        <button
                            onClick={() => scroll("left")}
                            className="hidden sm:flex absolute left-0 z-10 h-full px-1 sm:px-2 bg-white bg-opacity-90 hover:bg-opacity-100 items-center justify-center transition-all duration-200 shadow-sm"
                            aria-label="Scroll left"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-3 h-3 sm:w-4 sm:h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>
                    )}

                    {/* Scrollable tab container */}
                    <div
                        ref={scrollContainerRef}
                        className="flex h-full overflow-x-auto scrollbar-hide touch-pan-x"
                        style={{
                            scrollBehavior: "smooth",
                            WebkitOverflowScrolling: "touch" // Better touch scrolling on iOS
                        }}
                    >
                        <div className="flex h-full min-w-full sm:min-w-0">
                            {tabItems.map((item) => {
                                const isActive = item.key === currentTab;
                                return (
                                    // when we shift to new design then shift this <a> to <Link> for react rendering
                                    <a
                                        key={item.key}
                                        href={item.path}
                                        className={`
                                            h-10 sm:h-12 md:h-[36px]
                                            flex-shrink-0 flex items-center justify-center
                                            px-2 sm:px-3 md:px-4
                                            relative transition-all duration-200
                                            text-xs sm:text-sm md:text-base
                                            whitespace-nowrap
                                            ${isActive
                                                ? "text-[#740898] font-semibold sm:font-bold border-b-2 border-[#740898] bg-purple-50 sm:bg-transparent"
                                                : "text-[#262626] hover:text-[#740898] hover:bg-gray-50"
                                            }
                                        `}
                                        role="tab"
                                        aria-selected={isActive}
                                    >
                                        <span className="truncate max-w-[80px] sm:max-w-none">
                                            {item.label}
                                        </span>
                                    </a>
                                );
                            })}
                        </div>
                    </div>

                    {/* Right scroll button - hidden on mobile, visible on larger screens when needed */}
                    {showScrollButtons && (
                        <button
                            onClick={() => scroll("right")}
                            className="hidden sm:flex absolute right-0 z-10 h-full px-1 sm:px-2 bg-white bg-opacity-90 hover:bg-opacity-100 items-center justify-center transition-all duration-200 shadow-sm"
                            aria-label="Scroll right"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-3 h-3 sm:w-4 sm:h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    )}
                </div>
            )}

            {/* Content - Adjust padding when tabs are hidden */}
            <div className={`flex-grow min-h-screen bg-[#f9fafb] ${hideHeader ? 'pt-0' : 'p-4'}`}>
                {children}
            </div>
        </AppLayout>
    );
};

export default ProductsLayout;
