// @import url("https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700&display=swap");
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
//Colors SCSS
:root {
  --color-primary: #740898; //old color #2C4BFF
  --color-secondary: #000;
  --color-tertiary: #94a3b8;
}

$primary: var(--color-primary);
$secondary: var(--color-secondary);
$tertiary: var(--color-tertiary);

// Default
$black:       #252525;
$dark-black:  #1A1919;
$white:       #fff;
$ghost-white :#F9FBFF;
$light:       #cbd5e1;
$blue:        #740898#2C4BFF;
$dark-blue:   #1E3CEA;
$blue-light:  #F4F7FF;
$light-blue:  #E7ECFF;
$grey:        #6C757D;
$neutral800:  #191D23;
$neutral100:  #E7EAEE;
$dim-gray:    #5B5B5B;
$light-grey:  #C9C9CF;
$light300:   #F2F2F2;
$light-blue:  #F2F8FF;
$gains-boro:  #E6E6E6;
$white-smoke: #F8F8F8;
$silver:      #CACACA;
$dark-green:  #064E3B;
$light-green: #D7F8DE;
$success:     #28A745;
$red :        #DC3545;
$warning:     #FFC107;
$light-yellow:#FDD44B;
$orange:      #FF7E20;
$light-orange:#FFE1A8;
$lavender:    #EEEEEF;


// Font Famlily & Sizes
$primary-family: 'Roboto', sans-serif;
$secondary-family: 'Poppins', sans-serif;

@function toRem($value) {
  $remValue: ($value / 16) + rem;
  @return $remValue;
}

$primary-fs: 1rem; // 1rem => 16px
$secondary-fs: 0.875rem; // 0.875rem => 14px
$tertiary-fs: 0.75rem; // 0.75rem => 12px

$h1-font-size: toRem(24);
$h2-font-size: toRem(20);
$h3-font-size: toRem(16);
$h4-font-size: toRem(14);
$h5-font-size: toRem(12);
$h6-font-size: toRem(10);

// line height
$line-16: 1rem;
$line-18: 1.125rem;
$line-24: 1.5rem;
$line-28: 1.75rem;
$line-32: 2rem;
$line-34: 2.125rem;
$line-36: 2.25rem;
$line-48: 3rem;

// Media Quries
$breakpoint-xxl: "only screen and (min-width: 1440px)";
$breakpoint-1900-xxl: "only screen and (min-width: 1500px) and (max-width:1900px)";
$breakpoint-1500-xxl: "only screen and (min-width: 1440px) and (max-width:1580px)";
$breakpoint-xl: "only screen and (min-width: 1200px) and (max-width:1500px)";
$breakpoint-lg: "only screen and (min-width:992px) and (max-width:1199px)";
$breakpoint-lg-only: "only screen and (min-width:1200px)";
$breakpoint-lg-max: "only screen and (max-width:1199px)";
$breakpoint-md: "only screen and (min-width:768px) and (max-width:991px)";
$breakpoint-sm: "only screen and (min-width:576px) and (max-width:767px)";
$breakpoint-sm-only: "only screen and (max-width:991px)";
$breakpoint-xs: "only screen and (max-width:767px)";
$breakpoint-xxs: "only screen and (max-width: 375px)";
$breakpoint-xs-575: "only screen and (max-width: 575px)";
$breakpoint-above-xs: "only screen and (min-width: 767px)";
